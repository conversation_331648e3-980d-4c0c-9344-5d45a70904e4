{"version": 3, "file": "SpeakLiveClient.js", "sourceRoot": "", "sources": ["../../../src/packages/SpeakLiveClient.ts"], "names": [], "mappings": ";;;AAAA,6DAA0D;AAC1D,wCAA6C;AAG7C;;;;;;;;;;GAUG;AACH,MAAa,eAAgB,SAAQ,uCAAkB;IAGrD;;;;;;OAMG;IACH,YACE,OAA8B,EAC9B,eAA+C,EAAE,EACjD,WAAmB,gBAAgB;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QAdV,cAAS,GAAW,OAAO,CAAC;QAgBjC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;;OAOG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;gBACtB,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACtC,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAU,EAAE,EAAE;gBACjC,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACxC,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAiB,EAAE,EAAE;gBACxC,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACxC,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,KAAmB,EAAE,EAAE;gBAC5C,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC,CAAC;SACH;IACH,CAAC;IAED;;;OAGG;IACO,iBAAiB,CAAC,IAAS;QACnC,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAa,CAAC,QAAQ,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACzC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAa,CAAC,OAAO,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACxC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAa,CAAC,OAAO,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACxC;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SAC1C;IACH,CAAC;IAED;;;OAGG;IACO,mBAAmB,CAAC,IAAY;QACxC,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,IAAY;QAC1B,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO;YACb,IAAI;SACL,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO;SACd,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO;SACd,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO;SACd,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;OAGG;IACO,aAAa,CAAC,KAAmB;QACzC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;YAClC,IAAI;gBACF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACpC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;aAC9B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,KAAK,EAAE;oBAC7B,KAAK;oBACL,OAAO,EAAE,iCAAiC;oBAC1C,KAAK;iBACN,CAAC,CAAC;aACJ;SACF;aAAM,IAAI,KAAK,CAAC,IAAI,YAAY,IAAI,EAAE;YACrC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACvC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,KAAK,CAAC,IAAI,YAAY,WAAW,EAAE;YAC5C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;SACnD;aAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACtC;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,qBAAa,CAAC,KAAK,EAAE;gBAC7B,KAAK;gBACL,OAAO,EAAE,6BAA6B;aACvC,CAAC,CAAC;SACJ;IACH,CAAC;CACF;AAvJD,0CAuJC"}