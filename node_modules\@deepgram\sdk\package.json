{"name": "@deepgram/sdk", "version": "3.13.0", "description": "Isomorphic Javascript client for Deepgram", "keywords": ["javascript", "typescript", "deepgram", "asr", "speech", "sdk"], "homepage": "https://github.com/deepgram/deepgram-js-sdk", "bugs": "https://github.com/deepgram/deepgram-js-sdk/issues", "license": "MIT", "author": {"name": "Deepgram DevRel Team", "email": "<EMAIL>"}, "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "files": ["dist", "src"], "engines": {"node": ">=18.0.0"}, "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "sideEffects": false, "repository": "deepgram/deepgram-js-sdk", "scripts": {"clean": "rimraf dist docs/v2", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "build:umd": "webpack --mode=production", "lint": "run-s lint:*", "lint:js": "eslint --no-eslintrc  -c .eslintrc-examples.js examples --max-warnings 0", "lint:md": "markdownlint **/*.md *.md", "lint:yml": "yamllint .github/workflows", "lint:ts": "eslint src --max-warnings 0", "watch": "nodemon -e ts --watch src --exec \"npm run build\"", "test": "mocha -r ts-node/register test/*test.ts test/**/*test.ts --inspect --exit", "test:coverage": "nyc --reporter=lcovonly --reporter=text --reporter=text-summary npm run test", "docs": "typedoc --entryPoints src/index.ts --out docs/ --includes src/packages/**/*.ts   --emit none", "docs:json": "typedoc --entryPoints src/index.ts --includes src/packages/**/*.ts --json docs/spec.json   --emit none"}, "dependencies": {"@deepgram/captions": "^1.1.1", "@types/node": "^18.19.39", "cross-fetch": "^3.1.5", "deepmerge": "^4.3.1", "events": "^3.3.0", "ws": "^8.17.0"}, "devDependencies": {"@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^17.6.7", "@faker-js/faker": "^8.0.2", "@flydotio/dockerfile": "^0.4.10", "@types/chai": "^4.3.5", "@types/mocha": "^9.1.1", "@types/sinon": "^17.0.3", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "chai": "^4.3.7", "cross-env": "^7.0.3", "eslint": "^8.57.1", "husky": "^4.3.0", "markdownlint": "^0.35.0", "markdownlint-cli": "^0.42.0", "mocha": "^9.2.2", "nodemon": "^3.0.1", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "prettier": "^2.5.1", "pretty-quick": "^3.1.3", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "sinon": "^17.0.1", "ts-loader": "^8.0.11", "ts-node": "^10.9.1", "typedoc": "^0.22.16", "typescript": "^4.5.5", "webpack": "^5.69.1", "webpack-cli": "^4.9.2", "yaml-lint": "^1.7.0"}, "overrides": {"es5-ext": "0.10.53"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "jsdelivr": "dist/umd/deepgram.js", "unpkg": "dist/umd/deepgram.js"}