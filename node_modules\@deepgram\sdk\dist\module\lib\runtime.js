export const NODE_VERSION = typeof process !== "undefined" && process.versions && process.versions.node
    ? process.versions.node
    : "unknown";
export const BUN_VERSION = typeof process !== "undefined" && process.versions && process.versions.bun
    ? process.versions.bun
    : "unknown";
export const BROWSER_AGENT = typeof window !== "undefined" && window.navigator && window.navigator.userAgent
    ? window.navigator.userAgent
    : "unknown";
export const isBrowser = () => BROWSER_AGENT !== "unknown";
export const isNode = () => NODE_VERSION !== "unknown";
export const isBun = () => BUN_VERSION !== "unknown";
//# sourceMappingURL=runtime.js.map