{"version": 3, "file": "AgentLiveClient.js", "sourceRoot": "", "sources": ["../../../src/packages/AgentLiveClient.ts"], "names": [], "mappings": ";;;AAAA,gDAAqD;AACrD,0DAAuD;AACvD,0CAA8C;AAE9C,6DAA0D;AAE1D,MAAa,eAAgB,SAAQ,uCAAkB;IAGrD,YAAY,OAA8B,EAAE,WAAmB,0BAA0B;;QACvF,KAAK,CAAC,OAAO,CAAC,CAAC;QAHV,cAAS,GAAW,OAAO,CAAC;QAIjC,IAAI,CAAC,OAAO,GAAG,MAAA,MAAA,MAAA,MAAA,OAAO,CAAC,KAAK,0CAAE,SAAS,0CAAE,OAAO,0CAAE,GAAG,mCAAI,6BAAiB,CAAC;QAE3E,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;gBACtB,IAAI,CAAC,IAAI,CAAC,yBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAU,EAAE,EAAE;gBACjC,IAAI,CAAC,IAAI,CAAC,yBAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACtC,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAiB,EAAE,EAAE;gBACxC,IAAI,CAAC,IAAI,CAAC,yBAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACtC,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,KAAmB,EAAE,EAAE;gBAC5C,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC,CAAC;SACH;IACH,CAAC;IAED;;;OAGG;IACO,aAAa,CAAC,KAAmB;QACzC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;YAClC,IAAI;gBACF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACpC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;aAC9B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,yBAAW,CAAC,KAAK,EAAE;oBAC3B,KAAK;oBACL,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,iCAAiC;oBAC1C,KAAK;iBACN,CAAC,CAAC;aACJ;SACF;aAAM,IAAI,KAAK,CAAC,IAAI,YAAY,IAAI,EAAE;YACrC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACvC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,KAAK,CAAC,IAAI,YAAY,WAAW,EAAE;YAC5C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;SACnD;aAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACtC;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,yBAAW,CAAC,KAAK,EAAE;gBAC3B,KAAK;gBACL,OAAO,EAAE,6BAA6B;aACvC,CAAC,CAAC;SACJ;IACH,CAAC;IAED;;;OAGG;IACO,mBAAmB,CAAC,IAAY;QACxC,IAAI,CAAC,IAAI,CAAC,yBAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACO,iBAAiB,CAAC,IAAS;QACnC,IAAI,IAAI,CAAC,IAAI,IAAI,yBAAW,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC5B;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,yBAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SACxC;IACH,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,OAAwB;;QACvC,IACE,CAAC,CAAA,MAAA,OAAO,CAAC,KAAK,CAAC,MAAM,0CAAE,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;aAC1D,MAAA,MAAA,OAAO,CAAC,KAAK,CAAC,MAAM,0CAAE,QAAQ,CAAC,QAAQ,0CAAE,MAAM,CAAA,EAC/C;YACA,MAAM,IAAI,sBAAa,CAAC,qDAAqD,CAAC,CAAC;SAChF;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,iBAC3B,IAAI,EAAE,UAAU,IACb,OAAO,EACV,CAAC;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,MAAc;QAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,WAAkE;QACnF,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;IACzE,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CAAC,OAAe;QACvC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACI,oBAAoB,CAAC,QAA8B;QACxD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,iBAAG,IAAI,EAAE,sBAAsB,IAAK,QAAQ,EAAG,CAAC,CAAC;IAC3E,CAAC;IAED;;;;OAIG;IACI,SAAS;QACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;CACF;AA5JD,0CA4JC"}