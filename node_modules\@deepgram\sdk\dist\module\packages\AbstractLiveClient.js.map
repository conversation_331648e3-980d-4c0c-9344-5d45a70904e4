{"version": 3, "file": "AbstractLiveClient.js", "sourceRoot": "", "sources": ["../../../src/packages/AbstractLiveClient.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,kBAAkB,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAGnE,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AA+BvC;;;;;GAKG;AACH,iCAAiC;AACjC,gBAAgB;AAChB,qBAAqB;AACrB,kBAAkB;AAClB,IAAI;AAEJ;;GAEG;AACH,MAAM,0BAA0B,GAAG,OAAO,SAAS,KAAK,WAAW,CAAC;AAEpE;;;;;;GAMG;AACH,MAAM,OAAgB,kBAAmB,SAAQ,cAAc;IAM7D,YAAY,OAA8B;QACxC,KAAK,CAAC,OAAO,CAAC,CAAC;QAJV,SAAI,GAAyB,IAAI,CAAC;QAClC,eAAU,GAAe,EAAE,CAAC;QAyGnC;;;;WAIG;QACI,cAAS,GAAkC,IAAI,CAAC;QAzGrD,MAAM,EACJ,GAAG,EACH,SAAS,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,MAAM,EAAE,GACjD,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE1B,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,KAAM,CAAC,GAAG,CAAC;SAC5C;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,GAAG,CAAC;SACrC;QAED,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;SACzB;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACvB;QAED,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,gBAAgB,CAAC;SAClD;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;YACtC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC,oBAAoB;SACrE;IACH,CAAC;IAED;;;;OAIG;IACO,OAAO,CAAC,oBAAgC,EAAE,QAAgB;QAClE,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAO;SACR;QAED,IAAI,CAAC,SAAS,GAAG,CAAC,OAAO,GAAG,oBAAoB,EAAE,EAAE;YAClD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,EAAE,oBAAoB,CAAC,CAAC;QAE1E;;WAEG;QACH,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,EAAE;gBACpD,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YACH,OAAO;SACR;QAED;;;;;WAKG;QACH,IAAI,KAAK,EAAE,EAAE;YACX,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;gBACpC,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,UAAU,EAAE;oBAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBAChC,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC;YACH,OAAO;SACR;QAED;;WAEG;QACH,IAAI,0BAA0B,EAAE;YAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO;SACR;QAED;;WAEG;QACH,IAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,CAAC,UAAU,EAAE,SAAS,EAAE;YACtD,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACnB,CAAC;SACF,CAAC,CAAC;QAEH;;WAEG;QACH,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;YACpC,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE;gBACxC,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IASD;;;;;OAKG;IACI,UAAU,CAAC,IAAa,EAAE,MAAe;QAC9C,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,cAAa,CAAC,CAAC,CAAC,OAAO;YAC3C,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAC,CAAC;aACrC;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;IACH,CAAC;IAED;;;;OAIG;IACI,eAAe;QACpB,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACzC,KAAK,aAAa,CAAC,UAAU;gBAC3B,OAAO,gBAAgB,CAAC,UAAU,CAAC;YACrC,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,gBAAgB,CAAC,IAAI,CAAC;YAC/B,KAAK,aAAa,CAAC,OAAO;gBACxB,OAAO,gBAAgB,CAAC,OAAO,CAAC;YAClC;gBACE,OAAO,gBAAgB,CAAC,MAAM,CAAC;SAClC;IACH,CAAC;IAED;;;;OAIG;IACI,aAAa;;QAClB,OAAO,MAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,UAAU,mCAAI,aAAa,CAAC,MAAM,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,eAAe,EAAE,KAAK,gBAAgB,CAAC,IAAI,CAAC;IAC1D,CAAC;IAED;;;;;;OAMG;IACH,IAAI,CAAC,IAAoB;QACvB,MAAM,QAAQ,GAAG,GAAS,EAAE;;YAC1B,IAAI,IAAI,YAAY,IAAI,EAAE;gBACxB,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;oBACnB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,oCAAoC,EAAE,IAAI,CAAC,CAAC;oBAE7D,OAAO;iBACR;gBAED,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;aACjC;YAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,IAAI,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,CAAA,EAAE;oBACrB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,uCAAuC,EAAE,IAAI,CAAC,CAAC;oBAEhE,OAAO;iBACR;aACF;YAED,MAAA,IAAI,CAAC,IAAI,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAA,CAAC;QAEF,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,QAAQ,EAAE,CAAC;SACZ;aAAM;YACL,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChC;IACH,CAAC;IAED;;;OAGG;IACH,IAAI,KAAK;;QACP,OAAO,IAAI,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,CAAC,CAAA,MAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,0CAAE,GAAG,CAAA,CAAC;IACtF,CAAC;CAQF;AAED,MAAM,gBAAgB;IAWpB,YAAY,OAAY,EAAE,UAAqB,EAAE,OAA4B;QAV7E,eAAU,GAAW,aAAa,CAAC;QAEnC,YAAO,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC7B,YAAO,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC7B,cAAS,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC/B,WAAM,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC5B,eAAU,GAAW,aAAa,CAAC,UAAU,CAAC;QAC9C,SAAI,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC1B,QAAG,GAAwB,IAAI,CAAC;QAG9B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;CACF;AAED,OAAO,EAAE,kBAAkB,IAAI,gBAAgB,EAAE,CAAC"}