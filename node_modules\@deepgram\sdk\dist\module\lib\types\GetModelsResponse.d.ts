declare type Model = {
    name: string;
    canonical_name: string;
    architecture: string;
    languages?: string[];
    version: string;
    uuid: string;
    batch?: boolean;
    streaming?: boolean;
    formatted_output?: boolean;
    metadata?: {
        accent: string;
        color: string;
        image: string;
        sample: string;
    };
};
export declare type GetModelResponse = Model;
export declare type GetModelsResponse = {
    stt: Model[];
    tts: Model[];
};
export {};
//# sourceMappingURL=GetModelsResponse.d.ts.map