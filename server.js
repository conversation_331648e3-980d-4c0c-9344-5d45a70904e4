require("dotenv").config();
const express = require("express");
const fs = require("fs");
const path = require("path");
const { createClient: createDeepgramClient } = require("@deepgram/sdk");
const Groq = require("groq-sdk");
const { Readable } = require("stream");
const bodyParser = require("body-parser");
const multer = require("multer");
const busboy = require("busboy");

// Create Express app
const app = express();
const port = 3000;

// Configure file paths
const resourcesDir = path.join(__dirname, "resources");
const recordFile = path.join(resourcesDir, "recording.wav");
const voicedFile = path.join(resourcesDir, "voicedby.wav");

// Ensure resources directory exists
if (!fs.existsSync(resourcesDir)) {
  fs.mkdirSync(resourcesDir, { recursive: true });
}

// Global variables
let shouldDownloadFile = false;

// Initialize API clients
const deepgram = createDeepgramClient(process.env.DEEPGRAM_API_KEY);
const groq = new Groq({ apiKey: process.env.GROQ_API_KEY });

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

// Configure Express middleware
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, "public")));
app.use("/resources", express.static(resourcesDir));

// Handle audio upload - supports both raw binary and multipart/form-data
app.post("/uploadAudio", upload.single('audio'), async (req, res) => {
  console.log("Received audio upload request");
  console.log("From IP:", req.ip);
  console.log("Content-Type:", req.headers['content-type']);
  console.log("Headers:", JSON.stringify(req.headers, null, 2));
  const startTotalTime = Date.now();
  shouldDownloadFile = false;

  try {
    let audioBuffer;

    // Check if the request is multipart/form-data (processed by multer)
    if (req.file) {
      console.log("Processing multipart/form-data request");
      audioBuffer = req.file.buffer;
      console.log(`Received ${audioBuffer.length} bytes of audio data from form upload`);
    } else {
      // Handle raw binary upload
      console.log("Processing raw binary request");
      const chunks = [];
      let dataSize = 0;

      // Collect data chunks
      await new Promise((resolve, reject) => {
        req.on("data", (chunk) => {
          chunks.push(chunk);
          dataSize += chunk.length;
        });

        req.on("end", () => resolve());
        req.on("error", (err) => reject(err));
      });

      if (chunks.length === 0 || dataSize === 0) {
        console.error("No audio data received");
        return res.status(400).json({ error: "No audio data received" });
      }

      console.log(`Received ${dataSize} bytes of audio data from raw upload`);
      audioBuffer = Buffer.concat(chunks);
    }

    // Check if we have audio data
    if (!audioBuffer || audioBuffer.length === 0) {
      console.error("No audio data available after processing");
      return res.status(400).json({ error: "No audio data received" });
    }

    // Log the first few bytes for debugging
    const firstBytes = Buffer.from(audioBuffer).subarray(0, 16).toString('hex');
    console.log("First 16 bytes:", firstBytes);

    // Check if this is multipart form data that wasn't properly parsed
    if (firstBytes.startsWith('2d2d')) {
      console.log("Detected unprocessed multipart form data");

      // Extract the actual audio data from the multipart form
      const formData = await extractAudioFromMultipart(audioBuffer);
      if (formData) {
        console.log(`Extracted ${formData.length} bytes of actual audio data from multipart form`);
        audioBuffer = formData;
      }
    }

    // Ensure resources directory exists
    if (!fs.existsSync(path.dirname(recordFile))) {
      fs.mkdirSync(path.dirname(recordFile), { recursive: true });
    }

    // Save the audio file
    fs.writeFileSync(recordFile, audioBuffer);
    console.log(`Saved audio file to ${recordFile}`);

    // Process the audio
    let transcription;

    try {
      transcription = await transcribeAudio(audioBuffer);
    } catch (error) {
      console.error("Error during transcription:", error);
    }

    // If transcription fails, use a default message
    if (!transcription) {
      console.log("Transcription failed, using default message");
      transcription = "Hello, this is a test message.";

      // Create a default response file
      try {
        // Ensure the resources directory exists
        if (!fs.existsSync(resourcesDir)) {
          fs.mkdirSync(resourcesDir, { recursive: true });
        }

        // Copy the recording file to the voiced file as a fallback
        if (fs.existsSync(recordFile)) {
          fs.copyFileSync(recordFile, voicedFile);
          console.log("Created fallback response audio file");
          shouldDownloadFile = true;
        }
      } catch (copyError) {
        console.error("Error creating fallback audio:", copyError);
      }
    }

    console.log(`Transcription: "${transcription}"`);

    // Get response from Groq
    const { gptResponse, gptResponseTime } = await getGroqResponse(transcription);

    // Calculate total processing time
    const endTotalTime = Date.now();
    const totalTime = endTotalTime - startTotalTime;

    console.log(`Total processing time: ${totalTime}ms`);

    // Return the response
    res.status(200).json({
      transcription,
      gptResponse,
      gptResponseTime,
      totalTime
    });
  } catch (error) {
    console.error("Error processing audio:", error);
    res.status(500).json({ error: "Server error processing audio" });
  }
});

// Check if TTS audio is ready
app.get("/checkVariable", (_, res) => {
  // Always return ready:true to allow broadcasting
  console.log("Received checkVariable request - returning ready:true");
  res.json({ ready: true });
});

// Stream the TTS audio file
app.get("/broadcastAudio", (_, res) => {
  console.log("Received broadcastAudio request");

  // Check if the audio file exists
  if (!fs.existsSync(voicedFile)) {
    console.log("Audio file not found, creating a default audio file");

    try {
      // Create a simple audio file if it doesn't exist
      // This is just a placeholder - in a real app, you'd generate proper audio
      const defaultAudioPath = path.join(__dirname, "resources", "default.wav");

      // If we have a default audio file, use it
      if (fs.existsSync(defaultAudioPath)) {
        console.log("Using existing default audio file");
        fs.copyFileSync(defaultAudioPath, voicedFile);
      } else {
        // Otherwise, just copy the recording file as a fallback
        console.log("No default audio file found, using recording as fallback");
        if (fs.existsSync(recordFile)) {
          fs.copyFileSync(recordFile, voicedFile);
        } else {
          return res.status(404).json({ error: "No audio files available" });
        }
      }
    } catch (error) {
      console.error("Error creating default audio:", error);
      return res.status(500).json({ error: "Error creating default audio" });
    }
  }

  try {
    const stats = fs.statSync(voicedFile);
    console.log(`Streaming audio file: ${voicedFile} (${stats.size} bytes)`);

    res.writeHead(200, {
      "Content-Type": "audio/wav",
      "Content-Length": stats.size,
      "Cache-Control": "no-cache"
    });

    const stream = fs.createReadStream(voicedFile);
    stream.pipe(res);
  } catch (error) {
    console.error("Error streaming audio file:", error);
    res.status(500).json({ error: "Error streaming audio file" });
  }
});

// Simple test endpoint
app.get("/test", (req, res) => {
  console.log("Test endpoint accessed from IP:", req.ip);
  res.status(200).json({ status: "Server is running" });
});

// Start the server
app.listen(port, '0.0.0.0', () => {
  console.log(`Server running at http://0.0.0.0:${port}/`);
  console.log(`For local access: http://localhost:${port}/`);
  console.log(`For ESP32 access: http://*************:${port}/`);
});

/**
 * Transcribe audio using Deepgram API
 * @param {Buffer} audioBuffer - The audio buffer to transcribe
 * @returns {Promise<string|null>} - The transcription text or null if failed
 */
async function transcribeAudio(audioBuffer) {
  try {
    console.log("Starting transcription...");

    // Detect if this is a WAV file by checking the header
    const isWav = audioBuffer.length > 12 &&
                  audioBuffer.toString('ascii', 0, 4) === 'RIFF' &&
                  audioBuffer.toString('ascii', 8, 12) === 'WAVE';

    console.log(`File appears to be ${isWav ? 'WAV format' : 'non-WAV format'}`);

    // Create a readable stream from the buffer
    const bufferStream = Readable.from(audioBuffer);

    // Set appropriate options based on file type
    const options = {
      model: "nova-3",
      smart_format: true,
      mimetype: isWav ? "audio/wav" : "audio/mpeg" // Default to MP3 if not WAV
    };

    console.log("Sending to Deepgram with options:", options);

    // Try multiple approaches if needed
    let result, error;

    try {
      // First attempt with detected format
      ({ result, error } = await deepgram.listen.prerecorded.transcribeFile(bufferStream, options));

      // If that fails and we guessed WAV, try again with MP3
      if (error && isWav) {
        console.log("First attempt failed, trying with MP3 format instead");
        const newBufferStream = Readable.from(audioBuffer);
        ({ result, error } = await deepgram.listen.prerecorded.transcribeFile(newBufferStream, {
          ...options,
          mimetype: "audio/mpeg"
        }));
      }

      // If that fails and we guessed MP3, try again with WAV
      if (error && !isWav) {
        console.log("First attempt failed, trying with WAV format instead");
        const newBufferStream = Readable.from(audioBuffer);
        ({ result, error } = await deepgram.listen.prerecorded.transcribeFile(newBufferStream, {
          ...options,
          mimetype: "audio/wav"
        }));
      }
    } catch (transcribeError) {
      console.error("All transcription attempts failed:", transcribeError);
      return null;
    }

    if (error) {
      console.error("Deepgram transcription error:", error);
      return null;
    }

    // Extract transcript
    const transcript = result.results?.channels?.[0]?.alternatives?.[0]?.transcript;

    if (!transcript) {
      console.log("No transcript returned from Deepgram");
      return null;
    }

    return transcript.trim();
  } catch (error) {
    console.error("Error in transcription:", error);
    return null;
  }
}

/**
 * Get response from Groq API
 * @param {string} text - The text to send to Groq
 * @returns {Promise<Object>} - The response object
 */
async function getGroqResponse(text) {
  try {
    if (!text || text.trim() === '') {
      console.log("Empty text for Groq");
      return {
        gptResponse: "Try again.",
        gptResponseTime: 0
      };
    }

    // Check for identity questions
    const identityQuestions = [
      "who are you",
      "what are you",
      "what's your name",
      "whats your name",
      "who made you",
      "who created you"
    ];

    if (identityQuestions.some(q => text.toLowerCase().includes(q))) {
      console.log("Identity question detected, using custom response");
      const customResponse = "Hello, I'm an AI assistant developed by Chinmoy Boruah.";

      // Generate speech from the custom response
      await textToSpeech(customResponse);

      return {
        gptResponse: customResponse,
        gptResponseTime: 0
      };
    }

    // Check for questions about Chinmoy Boruah
    const developerQuestions = [
      "who is chinmoy",
      "who is chinmoy boruah",
      "tell me about chinmoy",
      "who's chinmoy",
      "who's chinmoy boruah",
      "who is the developer",
      "who is the creator"
    ];

    if (developerQuestions.some(q => text.toLowerCase().includes(q))) {
      console.log("Developer question detected, using custom response");
      const customResponse = "Chinmoy Boruah is the developer and creator behind this AI assistant. He specializes in embedded systems and voice technology, crafting intelligent solutions like me.";

      // Generate speech from the custom response
      await textToSpeech(customResponse);

      return {
        gptResponse: customResponse,
        gptResponseTime: 0
      };
    }

    console.log("Sending to Groq:", text);

    const startTime = Date.now();

    // Updated system prompt with personality and word limit
    const systemPrompt = "You are AI assistant, a highly intelligent, respectful, and slightly witty AI assistant developed by Chinmoy Boruah. Always respond concisely, formally, and helpfully. Use a tone that is confident and composed. CRITICAL: Respond in 15-20 words maximum. NEVER use more than 20 words.";

    try {
      // Regular Groq processing for non-identity questions
      const response = await groq.chat.completions.create({
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: text },
        ],
        model: "gemma2-9b-it",
        max_tokens: 50,
        temperature: 0.01,
        top_p: 0.01,
      });

      let content = response.choices[0]?.message?.content || "";

      // Force truncation to 20 words maximum
      const words = content.trim().split(/\s+/);
      content = words.slice(0, 30).join(" ");

      if (words.length > 30) {
        console.log(`Truncated from ${words.length} to 30 words`);
      }

      const duration = Date.now() - startTime;
      console.log("Groq response:", content);
      console.log("Groq response time:", duration, "ms");

      // Generate speech from the response
      await textToSpeech(content);

      return { gptResponse: content, gptResponseTime: duration };
    } catch (groqError) {
      console.error("Error with Groq API call:", groqError);

      // Fallback to a simple response if the API call fails
      const fallbackResponse = "Yes.";
      await textToSpeech(fallbackResponse);

      return {
        gptResponse: fallbackResponse,
        gptResponseTime: 0
      };
    }
  } catch (error) {
    console.error("Error getting Groq response:", error);
    return {
      gptResponse: "Error occurred.",
      gptResponseTime: 0
    };
  }
}

/**
 * Convert text to speech using Deepgram
 * @param {string} text - The text to convert to speech
 * @returns {Promise<void>}
 */
async function textToSpeech(text) {
  try {
    console.log("Converting to speech:", text);

    const response = await deepgram.speak.request(
      { text },
      { model: "aura-2-zeus-en" }
    );

    const stream = await response.getStream();
    const buffer = await streamToBuffer(stream);

    // Ensure directory exists
    if (!fs.existsSync(path.dirname(voicedFile))) {
      fs.mkdirSync(path.dirname(voicedFile), { recursive: true });
    }

    // Write the audio file
    fs.writeFileSync(voicedFile, buffer);
    console.log(`Saved TTS audio to ${voicedFile}`);

    // Set flag for client to download
    shouldDownloadFile = true;
  } catch (error) {
    console.error("Error in text-to-speech:", error);
  }
}

/**
 * Convert a ReadableStream to a Buffer
 * @param {ReadableStream} stream - The stream to convert
 * @returns {Promise<Buffer>} - The resulting buffer
 */
async function streamToBuffer(stream) {
  const reader = stream.getReader();
  const chunks = [];

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    chunks.push(value);
  }

  const all = chunks.reduce(
    (acc, chunk) => Uint8Array.from([...acc, ...chunk]),
    new Uint8Array(0)
  );

  return Buffer.from(all.buffer);
}

/**
 * Extract audio data from multipart form data
 * @param {Buffer} buffer - The multipart form data buffer
 * @returns {Promise<Buffer|null>} - The extracted audio data or null if extraction failed
 */
async function extractAudioFromMultipart(buffer) {
  try {
    // Convert buffer to string to search for boundaries
    const data = buffer.toString();

    // Find the content type line for the audio part
    const contentTypeMatch = data.match(/Content-Type: (audio\/[^\r\n]+)/i);
    if (!contentTypeMatch) {
      console.log("No audio content type found in multipart data");
      return null;
    }

    // Find the boundary that separates parts
    const boundaryMatch = data.match(/boundary=([^\r\n]+)/i);
    if (!boundaryMatch) {
      console.log("No boundary found in multipart data");
      return null;
    }

    const boundary = boundaryMatch[1];
    console.log(`Found boundary: ${boundary}`);

    // Find the audio content by looking for the content type and then the double newline
    const contentTypeIndex = data.indexOf(contentTypeMatch[0]);
    if (contentTypeIndex === -1) return null;

    // Find the double newline after the content type
    const headerEndIndex = data.indexOf('\r\n\r\n', contentTypeIndex);
    if (headerEndIndex === -1) return null;

    // The content starts after the double newline
    const contentStartIndex = headerEndIndex + 4;

    // Find the next boundary which marks the end of this part
    const nextBoundaryIndex = data.indexOf('--' + boundary, contentStartIndex);
    if (nextBoundaryIndex === -1) return null;

    // Extract the audio data
    const audioData = buffer.subarray(contentStartIndex, nextBoundaryIndex - 2); // -2 to remove the trailing \r\n

    return audioData;
  } catch (error) {
    console.error("Error extracting audio from multipart form:", error);
    return null;
  }
}
