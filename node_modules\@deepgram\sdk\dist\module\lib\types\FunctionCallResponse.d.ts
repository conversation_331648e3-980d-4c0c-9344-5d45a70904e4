/**
 * Respond with this when you receive a `FunctionCallRequest` payload.
 */
export interface FunctionCallResponse {
    /**
     * This must be the ID that was received in the request.
     */
    id: string;
    /**
     * The name of the function being called.
     */
    name: string;
    /**
     * The result of the function call.
     */
    content: string;
}
//# sourceMappingURL=FunctionCallResponse.d.ts.map